<template>
  <q-page padding class="row justify-between items-start">
    <div class="col-12">
      <q-card class="col-auto my-card">
        <q-card-section class="row items-center q-gutter-x-lg">
          <q-img id="avatar-img" src="https://cdn.quasar.dev/img/avatar4.jpg" alt="Avatar" />
          <div class="col q-gutter-y-md">
            <div class="text-h6">ผศ. เอกภพ คงกระพันธ์</div>
            <div class="">ชื่อ: เอกภพ คงกระพันธ์</div>
            <div class="">อายุปฏิบัติงาน: 4 ปี</div>
            <div class="">ตำแหน่ง: อาจารย์/ผู้ช่วยศาสตราจารย์</div>
          </div>
        </q-card-section>
      </q-card>
    </div>
    <div class="text-h6 q-mt-lg">แผนพัฒนาของฉัน</div>
    <!-- <q-timeline class="q-mt-sm" layout="dense" side="right">
      <q-timeline-entry
        v-for="(item, index) in 5"
        :key="index"
        :title="`กิจกรรมที่ ${index + 1}`"
        :subtitle="`รายละเอียดกิจกรรมที่ ${index + 1}`"
        icon="event"
        color="primary"
        timestamp="2023-10-01T12:00:00Z"
      >
        <q-timeline-entry-content>
          <div class="text-subtitle2">รายละเอียดเพิ่มเติมของกิจกรรมที่ {{ index + 1 }}</div>
          <p>
            นี่คือรายละเอียดเพิ่มเติมเกี่ยวกับกิจกรรมที่
            {{ index + 1 }} ที่สามารถแสดงข้อมูลเพิ่มเติมได้
          </p>
        </q-timeline-entry-content>
      </q-timeline-entry>
    </q-timeline> -->
  </q-page>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
#avatar-img {
  width: 150px;
  height: 150px;
  border-radius: $generic-border-radius / 2;
}

.my-card {
  width: 100%;
  max-width: 550px;
}
</style>
