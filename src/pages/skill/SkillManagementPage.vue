<template>
  <q-page padding class="q-gutter-y-lg">
    <TopHeaderTable
      title="จัดการความรู้และทักษะ"
      create-button-label="เพิ่มความรู้และทักษะ"
      @search="onSearchUpdate"
      @create="onClickAdd"
    />

    <q-table
      :rows="rows"
      :columns="skillManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="view-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn dense unelevated class="view-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { defineAsyncComponent } from 'vue';
import { skillManagementColumns } from 'src/data/table_columns';
import type { Skill } from 'src/types/models';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';

const $q = useQuasar();
// Mock data for skills
const mockSkills: Skill[] = [
  {
    id: 1,
    competencyId: 1,
    name: 'การวางแผนโครงการ',
    description: 'ความสามารถในการวางแผนขั้นตอนการดำเนินงานโครงการ',
    type: 'Technical Skill',
    career: 'Management',
  },
  {
    id: 2,
    competencyId: 1,
    name: 'การติดตามผลการดำเนินงาน',
    description: 'ทักษะในการควบคุมและติดตามความคืบหน้าของโครงการ',
    type: 'Management Skill',
    career: 'Management',
  },
  {
    id: 3,
    competencyId: 2,
    name: 'การนำเสนอต่อผู้บริหาร',
    description: 'ความสามารถในการนำเสนอข้อมูลและผลงานต่อผู้บริหารอย่างมีประสิทธิภาพ',
    type: 'Communication Skill',
    career: 'Communication',
  },
  {
    id: 4,
    competencyId: 2,
    name: 'การเขียนรายงาน',
    description: 'ทักษะในการเขียนรายงานที่ชัดเจนและเข้าใจง่าย',
    type: 'Communication Skill',
    career: 'Communication',
  },
  {
    id: 5,
    competencyId: 3,
    name: 'การวิเคราะห์ข้อมูล',
    description: 'ความสามารถในการประมวลผลและวิเคราะห์ข้อมูลเชิงตัวเลข',
    type: 'Analytical Skill',
    career: 'Analysis',
  },
  {
    id: 6,
    competencyId: 3,
    name: 'การแก้ไขปัญหาเชิงระบบ',
    description: 'ทักษะในการหาสาเหตุและแนวทางแก้ไขปัญหาอย่างเป็นระบบ',
    type: 'Problem Solving Skill',
    career: 'Analysis',
  },
  {
    id: 7,
    competencyId: 4,
    name: 'การสร้างแรงบันดาลใจ',
    description: 'ความสามารถในการสร้างแรงจูงใจและกระตุ้นทีมงาน',
    type: 'Leadership Skill',
    career: 'Management',
  },
  {
    id: 8,
    competencyId: 5,
    name: 'การใช้งาน Microsoft Office',
    description: 'ทักษะในการใช้โปรแกรม Word, Excel, PowerPoint อย่างชำนาญ',
    type: 'Technical Skill',
    career: 'IT',
  },
];

const rows = ref<Skill[]>(mockSkills);

const onClickEdit = (row: Skill) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/skill/SkillForm.vue')),
    componentProps: {
      title: 'แก้ไขความรู้และทักษะ',
      formData: row,
    },
    persistent: true,
  })
    .onOk((data: Skill) => {
      // Logic to handle updating the skill
      console.log('Updated skill data:', data);
      // Find and update the row in the table
      const index = rows.value.findIndex((item) => item.id === data.id);
      if (index !== -1) {
        rows.value[index] = { ...data };
      }
    })
    .onCancel(() => {
      console.log('Edit dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Edit dialog dismissed');
    });
};

const onClickDelete = (row: Skill) => {
  // Logic to handle delete action
  console.log('Delete row:', row);
};

const onClickAdd = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/skill/SkillForm.vue')),
    componentProps: {
      title: 'สร้างความรู้และทักษะใหม่',
    },
    persistent: true,
  })
    .onOk((data: Skill) => {
      // Logic to handle saving the new skill
      console.log('New skill data:', data);
      // Add new skill to the table
      const newId = Math.max(...rows.value.map((r) => r.id)) + 1;
      rows.value.push({ ...data, id: newId });
    })
    .onCancel(() => {
      console.log('Add dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Add dialog dismissed');
    });
};

const onSearchUpdate = (keyword: string) => {
  // Filter rows based on search keyword
  if (!keyword) {
    rows.value = mockSkills;
  } else {
    rows.value = mockSkills.filter(
      (item) =>
        item.name?.toLowerCase().includes(keyword.toLowerCase()) ||
        item.description?.toLowerCase().includes(keyword.toLowerCase()) ||
        item.type?.toLowerCase().includes(keyword.toLowerCase()) ||
        item.career?.toLowerCase().includes(keyword.toLowerCase()),
    );
  }
};
</script>

<style scoped></style>
