<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAuthStore } from 'src/stores/auth';

import { QIcon, QInput, QBtn, QForm } from 'quasar';
import { useRouter } from 'vue-router';
import MainFooter from 'src/components/MainFooter.vue';

const router = useRouter();
const visiblePassword = ref(false);
const authStore = useAuthStore();

const form = ref<QForm | null>(null);
const rememberMe = ref(false);

const login = async () => {
  const valid = await form.value?.validate();
  if (valid) {
    const success = await authStore.loginBuu();
    if (success) {
      // Check if there's a redirect path stored
      const redirectPath = localStorage.getItem('redirectAfterLogin');
      if (redirectPath && redirectPath !== '/login') {
        localStorage.removeItem('redirectAfterLogin');
        await router.replace(redirectPath);
      } else {
        await router.replace({ name: 'home' });
      }
    }
  }
};

onMounted(() => {
  // Reset all auth state and clear form fields when login page loads
  authStore.resetState();
  // Reset form validation state
  form.value?.resetValidation();
});
</script>

<template>
  <q-page
    class="q-pa-none login-page"
    style="min-height: 100vh; position: relative; overflow: hidden"
  >
    <div class="login-bg-gradient"></div>
    <div class="login-bg-logo">
      <q-img src="/brand/brand-logo.svg" style="width: 700px; height: 700px; object-fit: contain" />
    </div>

    <div class="login-form-outer">
      <div class="login-header-outer q-pb-none">
        <div class="login-logo-wrapper">
          <q-img src="/svg/burapha.svg" class="login-logo" />
        </div>
        <div class="login-title text-center">มหาวิทยาลัยบูรพา</div>
      </div>
      <div class="login-form-container">
        <div class="login-form-inner">
          <div class="login-subtitle flex items-center q-mb-lg">
            <q-icon name="lock" color="primary" class="q-mr-xs" size="20px" />
            <span class="text-bold" style="font-size: 18px">เข้าสู่ระบบ HRD</span>
          </div>
          <q-form ref="form" @submit="login()" class="q-gutter-y-md">
            <div class="login-label">อีเมล/รหัสพนักงาน</div>
            <q-input
              color="accent"
              dense
              outlined
              data-cy="login_username"
              label="ชื่อผู้ใช้"
              v-model="authStore.loginUsername"
              :error="authStore.incorrectUsernamePasswordStatus"
              :error-message="
                authStore.incorrectUsernamePasswordStatus
                  ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                  : undefined
              "
              :rules="[(val) => !!val || 'กรุณากรอกชื่อผู้ใช้']"
              @keyup.enter="login"
              class="login-input"
            >
              <template v-slot:prepend>
                <q-icon name="account_circle" color="grey-6"></q-icon>
              </template>
            </q-input>
            <div class="login-label" style="margin-top: 8px">รหัสผ่าน</div>
            <q-input
              color="accent"
              :type="visiblePassword ? 'text' : 'password'"
              dense
              outlined
              data-cy="login_password"
              label="รหัสผ่าน"
              v-model="authStore.loginPassword"
              :error="authStore.incorrectUsernamePasswordStatus"
              :error-message="
                authStore.incorrectUsernamePasswordStatus
                  ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                  : undefined
              "
              :rules="[(val) => !!val || 'กรุณากรอกรหัสผ่าน']"
              @keyup.enter="login"
              class="login-input"
            >
              <template v-slot:prepend>
                <q-icon name="key" color="grey-6"></q-icon>
              </template>
              <template v-slot:append>
                <q-icon
                  :data-cy="visiblePassword ? 'i-eye' : 'i-eyeOff'"
                  :name="visiblePassword ? 'visibility' : 'visibility_off'"
                  color="grey-4"
                  @click="visiblePassword = !visiblePassword"
                  class="cursor-pointer"
                ></q-icon>
              </template>
            </q-input>
            <q-checkbox v-model="rememberMe" label="จำฉันไว้ในระบบ" class="q-mt-xs" size="xs" />
            <q-btn
              type="submit"
              unelevated
              dense
              class="text-black full-width text-body1 login-btn"
              data-cy="login_btn"
              label="เข้าสู่ระบบ"
              color="primary"
            >
            </q-btn>
          </q-form>
          <div class="row justify-end q-mt-sm">
            <div class="col-auto">
              <a
                href="https://myid.buu.ac.th/"
                class="forgot-password-link"
                style="color: #ffb300; font-size: 14px; text-decoration: none"
                >ลืมรหัสผ่าน?</a
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <MainFooter class="footer-transparent" />
  </q-page>
</template>

<style>
html,
body,
#q-app {
  overflow: hidden !important;
  height: 100%;
}
/* Header logo & title outside the white box */
.login-header-outer {
  position: absolute;
  top: 110px;
  right: 58px;
  width: 448px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 4;
  pointer-events: none;
}
.login-logo-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0;
  margin-bottom: 8px;
  pointer-events: auto;
}
.login-logo {
  width: 120px;
  height: 120px;
}
.login-title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  margin-bottom: 8px;
  margin-top: 0;
  pointer-events: auto;
}
.login-subtitle {
  justify-content: center;
  margin-bottom: 24px;
  color: #222;
}
.login-bg-gradient {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-image: url('/mockup/BG.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}
.login-bg-logo {
  position: fixed;
  left: 0;
  top: 0;
  width: 60vw;
  height: 100vh;
  z-index: 2;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-form-container {
  position: absolute;
  top: 62%;
  right: 60px;
  transform: translateY(-50%);
  width: 448px;
  height: 450px;
  min-width: 352px;
  min-height: 400px;
  background: #fff;
  box-shadow: 0 4px 32px 0 rgba(31, 38, 135, 0.13);
  border-radius: 24px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.login-form-inner {
  width: 100%;
  max-width: 352px;
  margin: 0 auto;
  background: transparent;
  box-shadow: none;
  border-radius: 0;
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.login-label {
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1;
  color: #222;
  font-size: 15px;
}
.login-input {
  font-size: 16px;
}
.login-btn {
  width: 100%;
  margin-top: 12px;
  background: #ffcb05 !important;
  color: #222 !important;
  box-shadow: 0 2px 8px 0 rgba(255, 203, 5, 0.13);
  border-radius: 8px;
  letter-spacing: 0.5px;
}
.forgot-password-link {
  margin-left: auto;
  margin-right: 0;
  color: #ffb300;
  font-weight: 500;
  font-size: 14px;
  text-align: right;
  text-decoration: none;
  transition: color 0.2s;
}
.forgot-password-link:hover {
  color: #ff9800;
  text-decoration: underline;
}
.footer-transparent {
  background: transparent !important;
  box-shadow: none !important;
}
.login-form-outer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  justify-content: center;
  height: 100%;
}
@media (max-width: 600px) {
  .login-bg-gradient {
    background-image: url('/mockup/BG-PHONE.svg');
    background-size: cover;
    background-position: right center;
    display: block;
  }
  .login-bg-logo {
    display: none !important;
  }
  .login-form-outer {
    width: 100vw;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
  }
  .login-header-outer {
    position: static;
    top: unset;
    right: unset;
    width: 100vw;
    transform: none;
    margin-top: 24px;
    margin-bottom: 0;
    padding-bottom: 0;
    z-index: 4;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .login-form-container {
    position: static;
    top: unset;
    left: unset;
    right: unset;
    transform: none;
    width: 100vw;
    min-width: 0;
    max-width: 100vw;
    height: auto;
    background: transparent;
    box-shadow: none;
    border-radius: 0;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 16px;
    padding-bottom: 8px;
  }
  .login-form-inner {
    background: #fff;
    box-shadow: 0 10px 16px 0 rgba(31, 38, 135, 0.12);
    border-radius: 12px;
    margin: 12px 8px;
    padding: 12px 6px;
    max-width: 340px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .login-subtitle {
    font-size: 15px;
    margin-bottom: 16px;
    text-align: center;
    width: 100%;
    justify-content: center;
  }
  .login-btn {
    font-size: 16px;
    height: 36px;
  }
  .footer-transparent {
    display: none !important;
  }
}
</style>
