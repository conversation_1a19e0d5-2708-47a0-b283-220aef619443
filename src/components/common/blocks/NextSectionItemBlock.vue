<template>
  <div class="next-section-block">
    <!-- Section Navigation Dropdown -->
    <div class="row items-center q-gutter-md">
      <div class="col-auto">
        <q-icon name="arrow_forward" size="24px" color="primary" />
      </div>
      <div class="col">
        <q-select
          :model-value="currentNextSection"
          :options="sectionDropdownOptions"
          emit-value
          map-options
          outlined
          dense
          placeholder="เลือกส่วนที่ต้องการไป"
          style="min-width: 300px"
          @update:model-value="handleSectionSelect"
          :loading="isSaving"
          :disable="isSaving"
        >
          <template #selected>
            <span v-if="currentNextSection">{{ getSelectedSectionLabel() }}</span>
            <span v-else class="text-grey-6">เลือกส่วนที่ต้องการไป</span>
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>
    </div>

    <!-- Description text -->
    <div class="q-mt-md text-caption text-grey-7">
      เลือกส่วนที่ผู้ตอบแบบสอบถามจะไปต่อหลังจากเสร็จสิ้นส่วนปัจจุบัน
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import type { ItemBlock } from 'src/types/models';

// Props
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
  showSectionDropdowns?: boolean;
  availableSections?: Array<{
    label: string;
    value: number;
    headerTitle?: string;
  }>;
}>();

// Emits
const emit = defineEmits<{
  'update:option-next-section': [optionId: number, nextSection: number | null];
}>();

// Loading state
const isSaving = ref(false);

// Computed property for section dropdown options with Thai formatting
const sectionDropdownOptions = computed(() => {
  if (!props.availableSections || props.availableSections.length === 0) return [];

  // Add default option first
  const options: { label: string; value: number | null }[] = [
    {
      label: 'ดำเนินการต่อไปยังส่วนถัดไป',
      value: null,
    },
  ];

  // Add section options with Thai formatting
  props.availableSections.forEach((section) => {
    const headerTitle = section.headerTitle || '';
    const label = headerTitle
      ? `ไปที่ส่วนที่ ${section.value} (${headerTitle})`
      : `ไปที่ส่วนที่ ${section.value}`;

    options.push({
      label: label,
      value: section.value,
    });
  });

  return options;
});

// Get the current nextSection value from the first (and only) option
const currentNextSection = computed(() => {
  const firstOption = props.itemBlock.options?.[0];
  if (!firstOption) return null;

  // Explicitly handle null/undefined values - if nextSection is null, undefined, or 0, return null
  const nextSection = firstOption.nextSection;
  if (nextSection === null || nextSection === undefined || nextSection === 0) {
    return null;
  }

  return nextSection;
});

// Get the selected section label for display
function getSelectedSectionLabel(): string {
  const nextSection = currentNextSection.value;
  if (!nextSection) return 'ดำเนินการต่อไปยังส่วนถัดไป';

  const section = props.availableSections?.find((s) => s.value === nextSection);
  if (!section) return `ไปที่ส่วนที่ ${nextSection}`;

  const headerTitle = section.headerTitle || '';
  return headerTitle
    ? `ไปที่ส่วนที่ ${section.value} (${headerTitle})`
    : `ไปที่ส่วนที่ ${section.value}`;
}

// Handle section selection
async function handleSectionSelect(selectedSection: number | null) {
  const firstOption = props.itemBlock.options?.[0];
  if (!firstOption) {
    console.error('❌ No option found for NEXTSECTION block');
    return;
  }

  try {
    isSaving.value = true;

    // Emit the update to parent component
    emit('update:option-next-section', firstOption.id, selectedSection);
  } catch (error) {
    console.error('❌ Failed to update next section:', error);
  } finally {
    isSaving.value = false;
  }
}

// Create default option if none exists
async function ensureDefaultOption() {
  if (!props.itemBlock.options || props.itemBlock.options.length === 0) {
    try {
      const { OptionService } = await import('src/services/asm/optionService');
      const optionService = new OptionService();

      // Create a default option for the NEXTSECTION block
      const defaultOption = await optionService.createOption({
        itemBlockId: props.itemBlock.id,
        optionText: 'ไปยังส่วนถัดไป',
        value: 1,
      });

      console.log('✅ Created default option for NEXTSECTION block:', defaultOption);
    } catch (error) {
      console.error('❌ Failed to create default option:', error);
    }
  }
}

// Initialize component
onMounted(() => {
  ensureDefaultOption();
});
</script>

<style scoped>
.next-section-block {
  padding: 16px;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}
</style>
