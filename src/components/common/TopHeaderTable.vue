<template>
  <div class="top-header-table">
    <!-- Title -->
    <div class="text-h6 q-mb-md">{{ title }}</div>

    <!-- Top toolbar: Optional Search + Optional Create button -->
    <div class="row items-center q-gutter-sm justify-end q-mb-md">
      <SearchBar v-if="showSearch" @search="handleSearch" />
      <q-btn
        v-if="showCreateButton"
        :label="createButtonLabel"
        color="accent"
        icon="add"
        @click="handleCreate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchBar from 'src/components/SearchBar.vue';

interface Props {
  title: string;
  createButtonLabel?: string;
  showCreateButton?: boolean;
  showSearch?: boolean;
}

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
}

withDefaults(defineProps<Props>(), {
  createButtonLabel: 'สร้าง',
  showCreateButton: true,
  showSearch: true,
});

const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleCreate = () => {
  emit('create');
};
</script>

<style scoped lang="scss">
.top-header-table {
  margin-bottom: 16px;
}
</style>
