<script setup lang="ts">
import AppBreadcrumb from 'src/components/AppBreadcrumb.vue';
import MainFooter from 'src/components/MainFooter.vue';
import { useRoute } from 'vue-router';
import { computed, ref } from 'vue';
import { useGlobalStore } from 'src/stores/global';
import MainHeader from 'src/components/MainHeader.vue';
import LeftDrawer from 'src/components/common/LeftDrawer.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import type { MenuType } from 'src/types/app';
import { useAuthStore } from 'src/stores/auth';
import { roleMap } from 'src/data/roleMap';

const route = useRoute();
const globalStore = useGlobalStore();
const authStore = useAuthStore();
const confirmLogout = ref(false);

// Dynamically determine menu type based on current route path
const menuType = computed(() => {
  const queryType = route.query.type as MenuType;
  if (queryType) return queryType;

  const path = route.path;
  if (path.startsWith('/evaluate')) return 'form';
  if (path.startsWith('/quiz')) return 'quiz';
  if (path.startsWith('/ums')) return 'ums';
  if (path.startsWith('/competency')) return 'competency';
  if (path.startsWith('/skill')) return 'skill';
  if (path.startsWith('/idp')) return 'idp';
  if (path.startsWith('/in-house')) return 'in-house';

  return 'default';
});

const fullBreadcrumbItems = computed(() => globalStore.getBreadcrumbByType(menuType.value));

const breadcrumbItems = computed(() => {
  const currentPath = route.path;
  const currentHash = route.hash;
  const currentFullPath = currentPath + currentHash;
  const result = [];

  if (menuType.value === 'quiz' && /\/quiz\/do\/[\w-]+/.test(currentPath)) {
    const items = globalStore.getBreadcrumbByType('quiz');
    if (items[0]) result.push(items[0]); // หน้าหลัก
    if (items[2]) result.push(items[2]); // แบบทดสอบ
    if (items[3]) result.push(items[3]); // ใช้ items[3] (link, icon, etc. ครบ) แต่เปลี่ยน title
    return result.filter(Boolean);
  }

  // ✅ หน้า preview แบบสอบถาม (แก้ index เป็น 3)
  if (menuType.value === 'form' && /\/evaluate\/(\d+)\/(\d+)\/preview/.test(currentPath)) {
    const match = currentPath.match(/\/evaluate\/(\d+)\/(\d+)\/preview/);
    const evaluateId = match?.[1] || '';
    const sectionId = match?.[2] || '';
    const items = globalStore.getBreadcrumbByType('form');
    if (items[0]) result.push(items[0]);
    if (items[1]) result.push(items[1]);
    if (items[4]) {
      const previewItem = {
        ...items[4],
        link: globalStore.resolveBreadcrumbLink(items[4].link, evaluateId, sectionId),
      };
      result.push(previewItem);
    }
    return result.filter(Boolean);
  }

  // ✅ สำหรับทำแบบสอบถาม (evaluate)
  if (
    menuType.value === 'form' &&
    /\/evaluate\/user\/do-evaluate\/(\d+)\/(\d+)/.test(currentPath)
  ) {
    const items = globalStore.getBreadcrumbByType('form');
    if (items[0]) result.push(items[0]); // หน้าหลัก
    if (items[1]) result.push(items[1]); // ระบบจัดการแบบสอบถาม
    if (items[2]) result.push(items[2]); // ทำแบบสอบถาม
    return result.filter(Boolean);
  }

  if (menuType.value === 'form' && currentPath === '/evaluate/user') {
    const items = globalStore.getBreadcrumbByType('form');
    if (items[0]) result.push(items[0]); // หน้าหลัก
    if (items[2]) result.push(items[2]); // แบบสอบถาม
    return result.filter(Boolean);
  }

  if (menuType.value === 'form' && /\/evaluate\/do\/[\w-]+\/(\d+)/.test(currentPath)) {
    const items = globalStore.getBreadcrumbByType('form');
    if (items[0]) result.push(items[0]); // หน้าหลัก
    if (items[2]) result.push(items[2]); // แบบสอบถาม
    if (items[3]) result.push(items[3]); // ทำแบบสอบถาม
    return result.filter(Boolean);
  }

  // ✅ หน้า preview แบบทดสอบ
  if (menuType.value === 'quiz' && /\/quiz\/([\w-]+)\/preview/.test(currentPath)) {
    const match = currentPath.match(/\/quiz\/([\w-]+)\/preview/);
    const quizId = match?.[1] || '';
    const items = globalStore.getBreadcrumbByType('quiz');
    if (items[0]) result.push(items[0]);
    if (items[1]) result.push(items[1]);
    if (items[4]) {
      const previewItem = {
        ...items[4],
        link: items[4].link.replace(':quizId', quizId),
      };
      result.push(previewItem);
    }
    return result.filter(Boolean);
  }

  // ✅ หน้า /quiz/user โดยตรง
  if (menuType.value === 'quiz' && currentPath === '/quiz/user') {
    const items = globalStore.getBreadcrumbByType('quiz');
    if (items[0]) result.push(items[0]);
    if (items[2]) result.push(items[2]); // แบบทดสอบ
    return result.filter(Boolean);
  }

  if (fullBreadcrumbItems.value.length > 0) {
    const home = fullBreadcrumbItems.value[0];
    if (home) result.push(home);
  }

  // Special handling for edit pages - include management breadcrumb
  const isEditPage = currentPath.includes('/edit');

  for (let i = 1; i < fullBreadcrumbItems.value.length; i++) {
    const item = fullBreadcrumbItems.value[i];
    // Check for exact match with full path including hash
    if (currentFullPath === item?.link || currentPath === item?.link) {
      result.push(item);
      break;
    }

    // Check for startsWith match (for paths like /evaluate/management)
    if (item?.link && currentPath.startsWith(item.link)) {
      result.push(item);
      // Don't break here - continue to find more specific matches
    }

    // For edit pages, include the management breadcrumb even if path doesn't start with it
    if (
      isEditPage &&
      item?.link &&
      item.link.includes('/management') &&
      !item.link.includes('/management/')
    ) {
      result.push(item);
    }

    // Enhanced matching for dynamic routes like /evaluate/{id}/edit with hash
    if (item?.link && item.link.includes('/edit') && currentPath.includes('/edit')) {
      const itemLinkParts = item.link.split('#');
      const itemPath = itemLinkParts[0];
      const itemHash = itemLinkParts[1];

      if (itemPath) {
        const linkParts = itemPath.split('/');
        const pathParts = currentPath.split('/');

        const pathMatches =
          linkParts.length === pathParts.length - 1 &&
          linkParts[1] === pathParts[1] &&
          linkParts[2] === pathParts[3];

        // Check if hash matches (if item has hash)
        const hashMatches = !itemHash || currentHash === `#${itemHash}`;

        if (pathMatches && hashMatches) {
          result.push(item);
          break;
        }
      }
    }
  }

  return result;
});

const activeMenu = computed(() => {
  const roleName = authStore.currentRoleName ?? 'Standard User';
  return roleMap[roleName] || [];
});

const handleLogout = () => {
  authStore.logout(); // เรียก logout จาก authStore
};
</script>

<template>
  <q-layout view="hHh lpR fff">
    <MainHeader />
    <q-page-container class="page-container">
      <LeftDrawer :menu="activeMenu" @logout="confirmLogout = true" />
      <AppBreadcrumb :items="breadcrumbItems" class="q-px-lg q-py-md" />
      <div class="scrollable-content">
        <router-view />
      </div>
    </q-page-container>

    <MainFooter />
    <ConfirmDialog v-model="confirmLogout" title="ยืนยันการออกจากระบบ" @confirm="handleLogout" />
  </q-layout>
</template>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.scrollable-content {
  flex: 1 1 auto;
  overflow-y: auto;
}
</style>
