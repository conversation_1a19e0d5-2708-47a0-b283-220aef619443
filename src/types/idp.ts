import type { User, Role, Permission } from './models';

// Updated interfaces based on ERD schema
export interface Program {
  id: number;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  ownerId: number;
  creatorId: number;
  owner: User;
  creator: User;
  enrollments?: Enrollment[];
  programSkills?: ProgramSkill[];
  logPrograms?: LogProgram[];
}

export interface Skill {
  id: number;
  name: string;
  description: string;
  skillType: 'GENERAL' | 'SPECIAL' | 'EXECUTIVE';
  careerType: 'ACADEMIC' | 'SUPPORT' | null;
  programId: number;
  evaluatorId: number;
  program: Program;
  evaluator: User;
  userSkills?: UserSkill[];
  certificates?: Certificate[];
  programSkills?: ProgramSkill[];
  competencySkills?: CompetencySkill[];
  elearningSkills?: ElearningSkill[];
  careerSkillPlans?: CareerSkillPlan[];
  permAuditSkills?: PermAuditSkill[];
  logSkills?: LogSkill[];
}

export interface Competency {
  id: number;
  name: string;
  description: string | null;
  compType: 'GENERAL' | 'SPECIAL' | 'EXECUTIVE';
  careerType: 'ACADEMIC' | 'SUPPORT' | null;
  competencySkills?: CompetencySkill[];
  auditUserSkillCompetencies?: AuditUserSkillCompetency[];
  logCompetencies?: LogCompetency[];
}

export interface Career {
  id: number;
  role: string;
  careerType: string;
  careerName: string;
  careerRank: string;
  careerRecords?: CareerRecord[];
  careerSkillPlans?: CareerSkillPlan[];
  permissionCareers?: PermissionCareer[];
}

export interface DevelopmentPlan {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  originalPlanId: number | null;
  originalPlan: DevelopmentPlan | null;
  childPlans?: DevelopmentPlan[];
  typePlans?: TypePlan[];
  logDevelopmentPlans?: LogDevelopmentPlan[];
}

export interface TypePlan {
  id: number;
  name: 'GENERAL' | 'EXECUTIVE_GENERAL' | 'EXECUTIVE_SPECIAL' | 'SPECIAL' | 'SUPPORT' | 'POSITION';
  ageWork: 'LESS1' | '1-2YEAR' | '3-5YEAR' | '6-8YEAR' | '9UP' | null;
  facId: string | null;
  developmentPlanId: number;
  privateUserId: number | null;
  developmentPlan: DevelopmentPlan;
  privateUser: User | null;
  careerSkillPlans?: CareerSkillPlan[];
}

export interface Enrollment {
  id: number;
  joinDate: Date;
  cancelDate: Date | null;
  programId: number;
  userId: number;
  program: Program;
  user: User;
}

export interface UserSkill {
  id: number;
  isExtra: boolean;
  status: 'WAIT_APPROVE' | 'APPROVED' | 'DENY' | 'WAIT_EDIT' | 'REMOVE';
  approvedAt: Date;
  skillId: number;
  userId: number;
  skill: Skill;
  user: User;
  auditUserSkills?: AuditUserSkill[];
}

export interface CareerRecord {
  id: number;
  startDate: Date;
  endDate: Date | null;
  careerId: number;
  userId: number;
  career: Career;
  user: User;
}

export interface Certificate {
  id: number;
  name: string;
  filePath: string;
  ref: string | null;
  updateAt: Date;
  skillId: number;
  uploadById: number;
  skill: Skill;
  uploadBy: User;
  auditUserSkills?: AuditUserSkill[];
}

export interface CareerSkillPlan {
  id: number;
  typePlanId: number;
  skillId: number;
  careerId: number | null;
  typePlan: TypePlan;
  skill: Skill;
  career: Career | null;
}

export interface Criteria {
  id: number;
  name: string;
  description: string;
  skillCriterias?: SkillCriteria[];
}

export interface PermAuditSkill {
  id: number;
  createdAt: Date;
  userId: number;
  skillId: number;
  user: User;
  skill: Skill;
}

export interface AuditUserSkill {
  id: number;
  note: string;
  status: 'WAIT_APPROVE' | 'APPROVED' | 'DENY' | 'WAIT_EDIT' | 'REMOVE';
  createAt: Date;
  userSkillId: number;
  certId: number | null;
  facId: string;
  createdById: number | null;
  userSkill: UserSkill;
  certificate: Certificate | null;
  createdBy: User | null;
}

export interface AuditUserSkillCompetency {
  id: number;
  isPass: boolean;
  note: string;
  createAt: Date;
  competencyId: number;
  auditCreatedById: number;
  competency: Competency;
  auditCreatedBy: User;
}

export interface Criteria {
  id: number;
  name: string;
  description: string;
}

export interface SkillCriteria {
  id: number;
  isPass: boolean;
  criteriaId: number;
  auditSkillCompetencyId: number;
  criteria: Criteria;
  auditSkillCompetency: AuditUserSkillCompetency;
}

export interface PermissionCareer {
  id: number;
  careerId: number;
  permId: number;
  career: Career;
  permission: Permission;
}

export interface LogUser {
  id: number;
  actionAt: Date;
  action: 'create' | 'update' | 'delete';
  userId: number;
  actionById: number;
  user: User;
  actionBy: User;
}

export interface LogSkill {
  id: number;
  actionAt: Date;
  action: 'create' | 'update' | 'delete';
  skillId: number;
  actionById: number;
  skill: Skill;
  actionBy: User;
}

export interface LogCompetency {
  id: number;
  actionAt: Date;
  action: 'create' | 'update' | 'delete';
  competencyId: number;
  actionById: number;
  competency: Competency;
  actionBy: User;
}

export interface LogDevelopmentPlan {
  id: number;
  actionAt: Date;
  action: 'create' | 'update' | 'delete';
  hrdPlanId: number;
  actionById: number;
  hrdPlan: DevelopmentPlan;
  actionBy: User;
}

export interface LogProgram {
  id: number;
  actionAt: Date;
  action: 'create' | 'update' | 'delete';
  programId: number;
  actionById: number;
  program: Program;
  actionBy: User;
}

export interface ProgramSkill {
  id: number;
  programId: number;
  skillId: number;
  program: Program;
  skill: Skill;
}

export interface CompetencySkill {
  id: number;
  competencyId: number;
  skillId: number;
  competency: Competency;
  skill: Skill;
}

export interface ElearningSkill {
  id: number;
  eLearningId: number | null;
  skillId: number;
  skill: Skill;
}
